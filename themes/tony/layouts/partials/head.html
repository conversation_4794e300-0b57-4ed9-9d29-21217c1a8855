<head>
    <meta charset="UTF-8">

    <meta http-equiv="X-UA-Compatible" content="ie=edge">

    {{ hugo.Generator }}

    <!-- Responsive Web Design -->
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <!-- Avoid Automatic Format Detection -->
    <meta name="format-detection" content="telephone=no, date=no, address=no, email=no" />
    <!-- Avoid Transcoding -->
    <meta http-equiv="Cache-Control" content="no-transform" />
    <!-- Avoid Baidu Mobile Search Transcoding -->
    <meta http-equiv="Cache-Control" content="no-siteapp" />

    <!-- CSS -->
    {{- partial "style.html" . }}

    <link rel="shortcut icon" href="{{ $.Site.Params.siteLogo | relURL }}" type="image/x-icon" />
    <title>{{ .Title }}</title>

    <meta name="author" content="{{ .Params.author | default .Site.Params.author }}" />
    {{- $description := .Description | default .Site.Params.siteDescription | plainify -}}
    <meta name="description" content="{{ $description }}" />

    {{- with .Site.Params.googleSiteVerification }}
    <meta name="google-site-verification" content="{{ . }}" />
    {{- end }}

    {{- if .Site.Params.enableGoogleAnalytics }}
    <link rel="preconnect" href="https://www.google-analytics.com" crossorigin />

    {{ partial "third-party/google-analytics.html" . }}
    {{- end }}

    {{- if .Site.Params.enableWaline }}
    <link rel="stylesheet" href="https://unpkg.com/@waline/client@latest/dist/waline.css" />
    <link rel="stylesheet" href="https://unpkg.com/katex@latest/dist/katex.min.css" />
    <style>
        .wl-emoji {
            height: 20px !important;
            width: 20px !important;
            position: relative;
            top: 4px;
        }
        .wl-emoji-popup.display,
        .wl-gif-popup.display {
            position: relative !important;
            inset-inline-start: 0 !important;
        }
    </style>
    {{- end }}

    <!-- SEO -->
    <link rel="canonical" href="{{ .Permalink }}" />
    {{- $Deliver := . -}}

    {{ partial "custom/head-native-css.html" . }}

    {{ $cdnURL := "/" }}
    {{ $description := "" }}
    {{ $keywords := "" }}

    <link rel="stylesheet" href="{{ $cdnURL }}css/bootstrap.min.css" />
    <link rel="stylesheet" href="{{ $cdnURL }}css/remixicon/remixicon.css" />
    <link rel="stylesheet" href="{{ $cdnURL }}css/toc.css" />

</head>