<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        p {
            display: inline;
        }

        .qmoji {
            width: 20px;
            height: 20px;
            display: inline-block;
            position: relative;
            top: 4px;
        }
    </style>
    <title>Document</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/bodymovin/5.13.0/lottie.min.js"
        integrity="sha512-uOtp2vx2X/5+tLBEf5UoQyqwAkFZJBM5XwGa7BfXDnWR+wdpRvlSVzaIVcRe3tGNsStu6UMDCeXKEnr4IBT8gA=="
        crossorigin="anonymous" referrerpolicy="no-referrer"></script>
</head>

<body>
    <h1>测试</h1>
    <p>666<div id="lottie" class="qmoji"></div>Super Qmoji test(inline as an emoji)</p>
    <br><br>
    <p>666<img src="https://koishi.js.org/QFace/assets/qq_emoji/resfile/emoji/5/apng/5.png" class="qmoji"></img>Normal
        Qmoji
        test(inline as an emoji)</p>
    <br><br>
    <p>摘抄一段已有长段落 without Qmoji：2023.8，我成功步入高中生活。经历了 2023.Q3 & Q4 的生活体验，我切实地体会到了老师们的有趣（当学生这么多年，头一次见到拥有这么多口头禅的老师/doge），又在 Q4 的某一天度娘了一番，找到了某乎上本校的新人问话帖，也看到了我的各位老师，但是发现各位学长们对老师的介绍相当地潦草，于是有了为老师们留下一个 " 档案 " 的念头。2024.2.13 14 时左右，本人正在 surfing the Internet，然后就脑袋搭错一根筋一时兴起，加上之前蓄谋已久酝酿良久的想法，就准备再次入手一个 blog。</p>
    <br><br>
    <p class="withsuper">摘抄一段已有长段落 with Super Qmoji：2023.8，我成功步入高中生活。经历了 2023.Q3 & Q4 的生活体验，我切实地体会到了老师们的有趣（当学生这么多年，头一次见到拥有这么多口头禅的老师<div id="lottie2" class="qmoji"></div>），又在 Q4 的某一天度娘了一番，找到了某乎上本校的新人问话帖，也看到了我的各位老师，但是发现各位学长们对老师的介绍相当地潦草，于是有了为老师们留下一个 " 档案 " 的念头。2024.2.13 14 时左右，本人正在 surfing the Internet，然后就脑袋搭错一根筋一时兴起，加上之前蓄谋已久酝酿良久的想法，就准备再次入手一个 blog。</p>
    <br><br>
    <p>摘抄一段已有长段落 with Normal Qmoji：2023.8，我成功步入高中生活。经历了 2023.Q3 & Q4 的生活体验，我切实地体会到了老师们的有趣（当学生这么多年，头一次见到拥有这么多口头禅的老师<img src="https://koishi.js.org/QFace/assets/qq_emoji/resfile/emoji/277/apng/277.png" class="qmoji"></img>），又在 Q4 的某一天度娘了一番，找到了某乎上本校的新人问话帖，也看到了我的各位老师，但是发现各位学长们对老师的介绍相当地潦草，于是有了为老师们留下一个 " 档案 " 的念头。2024.2.13 14 时左右，本人正在 surfing the Internet，然后就脑袋搭错一根筋一时兴起，加上之前蓄谋已久酝酿良久的想法，就准备再次入手一个 blog。</p>

    <script lang="javascript">
        var animation = bodymovin.loadAnimation({
            container: document.getElementById('lottie'),
            renderer: 'svg',
            loop: true,
            autoplay: true,
            path: 'https://koishi.js.org/QFace/assets/qq_emoji/resfile/emoji/317/lottie/317.json'
        });
        var animation = bodymovin.loadAnimation({
            container: document.getElementById('lottie2'),
            renderer: 'svg',
            loop: true,
            autoplay: true,
            path: 'https://koishi.js.org/QFace/assets/qq_emoji/resfile/emoji/317/lottie/317.json'
        });
    </script>

</body>

</html>