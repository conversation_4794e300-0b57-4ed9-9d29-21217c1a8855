{{- $text := .Text -}} {{- $isQQEmoji := false -}} {{- /* Check if this is a QQ
emoji pattern :/emoji_name: */ -}} {{- if and (hasPrefix $text ":/") (hasSuffix
$text ":") (gt (len $text) 3) -}} {{- $emojiName := substr $text 2 (sub (len
$text) 3) -}} {{- /* Comprehensive QQ Emoji mapping */ -}} {{- $emojiMap := dict
"微笑" "😊" "撇嘴" "😒" "色" "😍" "发呆" "😳" "得意" "😏" "流泪" "😢" "害羞"
"😳" "闭嘴" "🤐" "睡" "😴" "大哭" "😭" "尴尬" "😅" "发怒" "😠" "调皮" "😜"
"呲牙" "😁" "惊讶" "😲" "难过" "😞" "酷" "😎" "冷汗" "😰" "抓狂" "🤪" "吐" "🤮"
"偷笑" "🤭" "可爱" "🥰" "白眼" "🙄" "傲慢" "😤" "饥饿" "🤤" "困" "😪" "惊恐"
"😱" "流汗" "😅" "憨笑" "😄" "大兵" "🫡" "奋斗" "💪" "咒骂" "🤬" "疑问" "🤔"
"嘘" "🤫" "晕" "😵" "折磨" "😖" "衰" "😔" "骷髅" "💀" "敲打" "🔨" "再见" "👋"
"擦汗" "😓" "抠鼻" "🤏" "鼓掌" "👏" "糗大了" "😳" "坏笑" "😈" "左哼哼" "😤"
"右哼哼" "😤" "哈欠" "🥱" "鄙视" "🙄" "委屈" "🥺" "快哭了" "😢" "阴险" "😏"
"亲亲" "😘" "吓" "😨" "可怜" "🥺" "菜刀" "🔪" "西瓜" "🍉" "啤酒" "🍺" "篮球"
"🏀" "乒乓" "🏓" "咖啡" "☕" "饭" "🍚" "猪头" "🐷" "玫瑰" "🌹" "凋谢" "🥀"
"示爱" "💕" "爱心" "❤️" "心碎" "💔" "蛋糕" "🎂" "闪电" "⚡" "炸弹" "💣" "刀"
"🔪" "足球" "⚽" "瓢虫" "🐞" "便便" "💩" "月亮" "🌙" "太阳" "☀️" "礼物" "🎁"
"拥抱" "🤗" "强" "💪" "弱" "😔" "握手" "🤝" "胜利" "✌️" "抱拳" "🙏" "勾引" "😏"
"拳头" "✊" "差劲" "👎" "爱你" "😍" "NO" "🚫" "OK" "👌" "爱情" "💕" "飞吻" "😘"
"跳跳" "🦘" "发抖" "🥶" "怄火" "😡" "转圈" "🌀" "磕头" "🙇" "回头" "↩️" "跳绳"
"🪢" "挥手" "👋" "激动" "🤩" "街舞" "💃" "献吻" "😘" "左太极" "☯️" "右太极" "☯️"
"白眼笑" "😏" "比心" "🫶" "不你不想" "🙅" "打call" "📣" "滚" "🌀" "敬礼" "🫡"
"考虑中" "🤔" "狂笑" "🤣" "没眼看" "🙈" "面无表情" "😐" "呵呵" "😄" "哈哈" "😆"
"嗯" "😊" "嘿嘿" "😏" "笑脸" "😊" "开心" "😄" "高兴" "😊" "快乐" "😊" "生气"
"😠" "愤怒" "😡" "哭泣" "😭" "伤心" "😢" "无语" "😑" "郁闷" "😔" "疲劳" "😩"
"紧张" "😰" "兴奋" "🤩" "激动" "🤩" "惊喜" "😲" "疯狂" "🤪" "晕倒" "😵" "昏倒"
"😵" "睡觉" "😴" "做梦" "😴" "思考" "🤔" "想象" "💭" "恋爱" "😍" "喜欢" "😍"
"讨厌" "😒" "厌恶" "🤮" "害怕" "😨" "恐惧" "😱" "勇敢" "💪" "坚强" "💪" "加油"
"💪" "努力" "💪" "成功" "🎉" "庆祝" "🎉" "失败" "😞" "沮丧" "😞" "希望" "🌟"
"梦想" "✨" -}} {{- /* Check if this emoji name exists in our mapping */ -}} {{-
$emoji := index $emojiMap $emojiName -}} {{- if $emoji -}} {{- $isQQEmoji = true
-}}
<span class="qq-emoji" title=":/{{ $emojiName }}:" data-emoji="{{ $emojiName }}"
    >{{ $emoji }}</span
>
{{- end -}} {{- end -}} {{- /* If not a QQ emoji, render as normal code span */
-}} {{- if not $isQQEmoji -}}
<code>{{ .Text }}</code>
{{- end -}}
